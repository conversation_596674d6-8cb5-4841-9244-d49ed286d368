import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'multiple_platform_tools_interface.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import '../../../generated/locales.g.dart';
import '../components/BPColorUtil.dart';

class MultiplePlatformToolsIO implements MultiplePlatformTools {
  MultiplePlatformToolsIO();

  @override
  void open(String url) async {
    final uri = Uri.parse(url);
    // if (await canLaunchUrl(uri)) {
    await launchUrl(uri);
    // } else {
    //   throw 'Could not launch $url';
    // }
  }

  @override
  void showImagePicker({
    BuildContext? context,
    required Function(String? filePath, String? name, Uint8List? bytes) completion,
  }) async {
    if (Platform.isIOS) {
      // iOS: 保留现有的 ImagePicker 逻辑
      try {
        print('Do not remove this line');
        final pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery, imageQuality: 86);
        Uint8List? bytes;
        try {
          if (pickedFile != null) {
            bytes = await pickedFile.readAsBytes();
          }
        } catch (e) {}
        completion(pickedFile?.path, pickedFile?.name, bytes);
      } catch (e) {
        completion(null, null, null);
      }
    } else {
      // Android: 使用文件管理器，避免需要媒体权限
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.single.path != null) {
          final file = result.files.single;
          final filePath = file.path!;
          final fileName = file.name;

          try {
            final bytes = await File(filePath).readAsBytes();
            completion(filePath, fileName, bytes);
          } catch (e) {
            completion(null, null, null);
          }
        } else {
          completion(null, null, null);
        }
      } catch (e) {
        completion(null, null, null);
      }
    }
  }

  @override
  void showImageSourcePicker({
    BuildContext? context,
    required Function(String? filePath, String? name, Uint8List? bytes) completion,
  }) async {
    if (Platform.isIOS) {
      // iOS: Show action sheet with camera and gallery options
      try {
        final result = await Get.bottomSheet<ImageSource?>(
          Container(
            decoration: BoxDecoration(
              color: BPColor.background,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 20),
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 32),
                  _buildOptionTile(
                    icon: Icons.camera_alt,
                    iconColor: Colors.white,
                    title: LocaleKeys.common_camera.tr,
                    onTap: () => Get.back(result: ImageSource.camera),
                  ),
                  const SizedBox(height: 8),
                  _buildOptionTile(
                    icon: Icons.photo_library,
                    iconColor: Colors.white,
                    title: LocaleKeys.common_gallery.tr,
                    onTap: () => Get.back(result: ImageSource.gallery),
                  ),
                  const SizedBox(height: 24),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Get.back(),
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.grey[800],
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          LocaleKeys.common_cancel.tr,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
        );

        if (result != null) {
          final pickedFile = await ImagePicker().pickImage(
            source: result,
            imageQuality: 86,
          );

          Uint8List? bytes;
          try {
            if (pickedFile != null) {
              bytes = await pickedFile.readAsBytes();
            }
          } catch (e) {}
          completion(pickedFile?.path, pickedFile?.name, bytes);
        } else {
          completion(null, null, null);
        }
      } catch (e) {
        completion(null, null, null);
      }
    } else {
      // Android: 使用文件管理器，避免需要媒体权限
      showImagePicker(context: context, completion: completion);
    }
  }

  @override
  void showVideoPicker({
    Duration? maxDuration,
    required Function(String? filePath, String? name, Uint8List? bytes) completion,
  }) async {
    try {
      final pickedFile = await ImagePicker().pickVideo(maxDuration: maxDuration, source: ImageSource.gallery);
      Uint8List? bytes;
      final path = pickedFile?.path;
      try {
        if (path != null) {
          bytes = File(path).readAsBytesSync();
        }
      } catch (e) {}
      completion(pickedFile?.path, pickedFile?.name, bytes);
    } catch (e) {
      completion(null, null, null);
    }
  }

  @override
  void showMediaPicker({
    required Function(String? filePath, String? name, Uint8List? bytes) completion,
  }) async {
    try {
      final pickedFile = await ImagePicker().pickMedia();
      Uint8List? bytes;
      final path = pickedFile?.path;
      try {
        if (path != null) {
          bytes = File(path).readAsBytesSync();
        }
      } catch (e) {}
      completion(pickedFile?.path, pickedFile?.name, bytes);
    } catch (e) {
      completion(null, null, null);
    }
  }

  @override
  String? host() {
    return null;
  }

  Widget _buildOptionTile({
    required IconData icon,
    required Color iconColor,
    required String title,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.grey[900],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: iconColor,
                  size: 24,
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white54,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

MultiplePlatformTools getMultiplePlatformTools() => MultiplePlatformToolsIO();
